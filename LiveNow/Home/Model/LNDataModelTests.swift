//
//  LNDataModelTests.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/23.
//

import Foundation
import HandyJSON

/// 数据模型测试类
/// 用于验证API数据解析是否正确
class LNDataModelTests {
    
    /// 测试API返回数据的解析
    static func testApiDataParsing() {
        // 模拟API返回的数据
        let mockApiResponse = """
        {
            "code": 0,
            "data": {
                "current": 1,
                "hitCount": true,
                "pages": 5,
                "records": [
                    {
                        "age": 25,
                        "birthday": "1998-05-15",
                        "constellation": 3,
                        "country": "Indonesia",
                        "coverVideoUrl": "https://example.com/cover.mp4",
                        "followFlag": "1",
                        "gender": "female",
                        "groundFileName": "ground_001.jpg",
                        "headFileName": "avatar_001.jpg",
                        "hotAnchor": "1",
                        "id": 12345,
                        "incomeDiamond": 1500,
                        "isNeedMute": false,
                        "isTop": "1",
                        "isVirVideo": "0",
                        "language": "English",
                        "needMuteStr": "",
                        "nickName": "Sophia",
                        "normalHeadFileName": "normal_avatar_001.jpg",
                        "onlineStatus": "1",
                        "repeatId": 0,
                        "roomTitle": "Welcome to my room",
                        "showVideoUrl": "https://example.com/show.mp4",
                        "source": 1,
                        "unionId": 67890,
                        "userCode": "USER001",
                        "videoPrice": 50,
                        "vipFlag": "1",
                        "virVideoId": 0
                    }
                ],
                "searchCount": true,
                "size": 20,
                "total": 100
            },
            "msg": "success",
            "success": true
        }
        """
        
        // 测试完整API响应解析
        if let apiResponse = LNAnchorApiResponse.deserialize(from: mockApiResponse) {
            print("✅ API响应解析成功")
            print("   Code: \(apiResponse.code)")
            print("   Success: \(apiResponse.success)")
            print("   Message: \(apiResponse.msg)")
            
            if let data = apiResponse.data {
                print("   Current Page: \(data.current)")
                print("   Total Pages: \(data.pages)")
                print("   Total Records: \(data.total)")
                print("   Records Count: \(data.records.count)")
                
                // 测试主播模型
                if let firstAnchor = data.records.first {
                    testAnchorModel(firstAnchor)
                    testLiveStreamModelConversion(firstAnchor)
                }
            }
        } else {
            print("❌ API响应解析失败")
        }
    }
    
    /// 测试主播模型的计算属性
    private static func testAnchorModel(_ anchor: LNAnchorModel) {
        print("\n📋 测试主播模型计算属性:")
        print("   ID: \(anchor.id)")
        print("   原始昵称: '\(anchor.nickName)'")
        print("   显示名称: '\(anchor.displayName)'")
        print("   在线状态: '\(anchor.onlineStatus)' -> \(anchor.userStatus.displayText)")
        print("   国家: '\(anchor.country)' -> \(anchor.countryFlag)")
        print("   视频价格: \(anchor.videoPrice) -> '\(anchor.priceDisplay)'")
        print("   头像文件: '\(anchor.headFileName)' -> '\(anchor.avatarName)'")
        print("   支持视频通话: \(anchor.hasVideoCall)")
        print("   有礼物功能: \(anchor.hasGift)")
        print("   收入钻石: \(anchor.incomeDiamond)")
    }
    
    /// 测试转换为LNLiveStreamModel
    private static func testLiveStreamModelConversion(_ anchor: LNAnchorModel) {
        print("\n🔄 测试数据转换:")
        let liveStream = LNLiveStreamModel(from: anchor)
        
        print("   转换后ID: '\(liveStream.id)'")
        print("   转换后用户名: '\(liveStream.username)'")
        print("   转换后状态: \(liveStream.status.displayText)")
        print("   转换后国家旗帜: '\(liveStream.countryFlag)'")
        print("   转换后价格: '\(liveStream.pricePerMinute)'")
        print("   转换后头像: '\(liveStream.avatarImageName)'")
        print("   转换后视频通话: \(liveStream.hasVideoCall)")
        print("   转换后礼物: \(liveStream.hasGift)")
    }
    
    /// 测试边界情况
    static func testEdgeCases() {
        print("\n🧪 测试边界情况:")
        
        // 测试空数据
        let emptyAnchor = LNAnchorModel()
        print("   空模型显示名称: '\(emptyAnchor.displayName)'")
        print("   空模型用户状态: \(emptyAnchor.userStatus.displayText)")
        print("   空模型国家旗帜: '\(emptyAnchor.countryFlag)'")
        print("   空模型价格显示: '\(emptyAnchor.priceDisplay)'")
        print("   空模型头像名称: '\(emptyAnchor.avatarName)'")
        
        // 测试不同在线状态
        let testStatuses = ["0", "1", "2", "unknown"]
        for status in testStatuses {
            let anchor = LNAnchorModel()
            anchor.onlineStatus = status
            print("   状态 '\(status)' -> \(anchor.userStatus.displayText)")
        }
        
        // 测试不同国家
        let testCountries = ["indonesia", "philippines", "vietnam", "thailand", "palestine", "jordan", "unknown"]
        for country in testCountries {
            let anchor = LNAnchorModel()
            anchor.country = country
            print("   国家 '\(country)' -> '\(anchor.countryFlag)'")
        }
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🚀 开始数据模型测试\n")
        
        testApiDataParsing()
        testEdgeCases()
        
        print("\n✅ 所有测试完成")
    }
}

// MARK: - 使用示例
/*
 在需要测试的地方调用：
 LNDataModelTests.runAllTests()
 
 或者在ViewController的viewDidLoad中添加：
 #if DEBUG
 LNDataModelTests.runAllTests()
 #endif
 */
