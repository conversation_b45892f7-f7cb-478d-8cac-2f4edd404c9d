//
//  LNAnchorListViewModel.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/22.
//

import Foundation

// MARK: - 主播列表类型枚举
enum LNAnchorListType: Int {
    case hot = 0        // 热门
    case new = 1        // 最新
    case follow = 2     // 关注
    
    var apiTarget: (_ params: [String: Any]) -> LNApiAnchor {
        switch self {
        case .hot:
            return { params in .popularList(par: params) }
        case .new:
            return { params in .newList(par: params) }
        case .follow:
            return { params in .followList(par: params) }
        }
    }
}

// MARK: - ViewModel 协议
protocol LNAnchorListViewModelDelegate: AnyObject {
    func dataDidLoad(liveStreams: [LNLiveStreamModel])
    func dataDidFail(error: Error)
    func loadingStateChanged(isLoading: Bool)
}

// MARK: - 主播列表 ViewModel
class LNAnchorListViewModel {
    
    // MARK: - Properties
    weak var delegate: LNAnchorListViewModelDelegate?
    
    private let listType: LNAnchorListType
    private let pageSize: Int = 20
    
    private var liveStreams: [LNLiveStreamModel] = []
    private var currentPage: Int = 1
    private var hasMore: Bool = true
    private var isLoading: Bool = false {
        didSet {
            delegate?.loadingStateChanged(isLoading: isLoading)
        }
    }
    
    // MARK: - Initialization
    init(type: LNAnchorListType) {
        self.listType = type
    }
    
    // MARK: - Public Methods
    
    /// 获取当前数据
    func getCurrentData() -> [LNLiveStreamModel] {
        return liveStreams
    }
    
    /// 是否还有更多数据
    func getHasMore() -> Bool {
        return hasMore
    }
    
    /// 是否正在加载
    func getIsLoading() -> Bool {
        return isLoading
    }
    
    /// 加载数据（刷新）
    func loadData(refresh: Bool = true) {
        if isLoading {
            return
        }
        
        if refresh {
            currentPage = 1
            hasMore = true
        }
        
        _getAnchorList(current: currentPage)
    }
    
    /// 加载更多数据
    func loadMore() {
        if isLoading || !hasMore {
            return
        }
        
        currentPage += 1
        _getAnchorList(current: currentPage)
    }
    
    // MARK: - Private Methods
    
    /// 核心数据请求方法（对应 Flutter 的 _getAnchorList 方法）
    private func _getAnchorList(current: Int) {
        isLoading = true
        
        // 构建请求参数
        let params = [
            "current": "\(current)",
            "size": "\(pageSize)"
        ]
        
        // 根据类型选择API
        let apiTarget = listType.apiTarget(params)
        
        // 发起网络请求
        NetWorkRequest(apiTarget, completion: { [weak self] result in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.isLoading = false
                self.handleSuccess(result: result, current: current)
            }
            
        }, failure: { [weak self] error in
            guard let self = self else { return }
            
            DispatchQueue.main.async {
                self.isLoading = false
                self.handleFailure(error: error, current: current)
            }
        })
    }
    
    /// 处理成功响应
    private func handleSuccess(result: [String: Any], current: Int) {
        // 解析响应数据
        if let dataDict = result["data"] as? [String: Any],
           let jsonData = try? JSONSerialization.data(withJSONObject: dataDict),
           let pageResponse = LNPageResponse<LNUserModel>.deserialize(from: String(data: jsonData, encoding: .utf8)) {
            
            let newItems = pageResponse.records
            
            // 转换为 LNLiveStreamModel
            let newLiveStreams = newItems.map { userModel in
                convertUserToLiveStream(userModel)
            }
            
            // 更新数据
            if current == 1 {
                // 刷新数据
                liveStreams = newLiveStreams
            } else {
                // 追加数据
                liveStreams.append(contentsOf: newLiveStreams)
            }
            
            // 更新分页状态
            hasMore = pageResponse.current < pageResponse.pages
            
            // 通知代理
            delegate?.dataDidLoad(liveStreams: liveStreams)
            
        } else {
            // 数据解析失败
            let error = NSError(domain: "LNAnchorListViewModel", code: -1, userInfo: [NSLocalizedDescriptionKey: "数据解析失败"])
            handleFailure(error: error, current: current)
        }
    }
    
    /// 处理失败响应
    private func handleFailure(error: Error, current: Int) {
        // 如果不是第一页，需要回退页码
        if current > 1 {
            currentPage = max(1, currentPage - 1)
        }
        
        // 通知代理
        delegate?.dataDidFail(error: error)
    }
    
    /// 将 LNUserModel 转换为 LNLiveStreamModel
    private func convertUserToLiveStream(_ userModel: LNUserModel) -> LNLiveStreamModel {
        let status: LNUserStatus
        switch userModel.onlineStatus {
        case "1":
            status = .online
        case "2":
            status = .busy
        default:
            status = .offline
        }
        
        let countryFlag: String
        switch userModel.country.lowercased() {
        case "palestine", "ps":
            countryFlag = "🇵🇸"
        case "jordan", "jo":
            countryFlag = "🇯🇴"
        case "indonesia", "id":
            countryFlag = "🇮🇩"
        case "philippines", "ph":
            countryFlag = "🇵🇭"
        case "vietnam", "vn":
            countryFlag = "🇻🇳"
        case "thailand", "th":
            countryFlag = "🇹🇭"
        default:
            countryFlag = "🇺🇸"
        }
        
        return LNLiveStreamModel(
            id: "\(userModel.id)",
            username: userModel.nickName.isEmpty ? "NIKENAME" : userModel.nickName,
            status: status,
            countryFlag: countryFlag,
            pricePerMinute: userModel.videoPrice > 0 ? "\(userModel.videoPrice)/min" : "60/min",
            avatarImageName: userModel.headFileName.isEmpty ? "avatar\(userModel.id % 6 + 1)" : userModel.headFileName,
            hasVideoCall: userModel.freeVideoCall > 0 || userModel.videoPrice > 0,
            hasGift: userModel.giveDiamond > 0
        )
    }
}
