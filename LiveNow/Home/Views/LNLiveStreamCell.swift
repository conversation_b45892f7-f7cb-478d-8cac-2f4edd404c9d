//
//  LNLiveStreamCell.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import UIKit
import SnapKit

/// 直播流卡片 Cell
class LNLiveStreamCell: UICollectionViewCell {
    
    // MARK: - UI Elements
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = s(12)
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.layer.cornerRadius = s(8)
        imageView.layer.masksToBounds = true
        imageView.backgroundColor = UIColor.lightGray
        return imageView
    }()
    
    private lazy var statusIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        view.layer.cornerRadius = s(10)
        view.layer.masksToBounds = true
        return view
    }()

    private lazy var statusDot: UIView = {
        let view = UIView()
        view.layer.cornerRadius = s(3)
        view.layer.masksToBounds = true
        return view
    }()

    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.medium(s(10))
        label.textColor = .white
        label.textAlignment = .left
        return label
    }()
    
    private lazy var countryFlagImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .white
        imageView.layer.cornerRadius = s(8)
        imageView.layer.masksToBounds = true
        return imageView
    }()
    
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.bold(s(16))
        label.textColor = .white
        label.textAlignment = .left
        return label
    }()
    
    private lazy var diamondImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "ic_diamond")
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = UIColor.hex(hexString: "#00E5A0")
        return imageView
    }()

    private lazy var priceLabel: UILabel = {
        let label = UILabel()
        label.font = LNFont.regular(14)
        label.textColor = UIColor.white
        label.textAlignment = .left
        return label
    }()
    
    private lazy var actionButton: UIButton = {
        let button = UIButton(type: .custom)
        button.layer.cornerRadius = s(25)
        button.layer.masksToBounds = true
        return button
    }()
    
    private lazy var giftImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.isHidden = true
        return imageView
    }()
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Private Methods
    private func setupUI() {
        contentView.addSubview(containerView)
        
        containerView.addSubview(avatarImageView)
        containerView.addSubview(statusIndicator)
        statusIndicator.addSubview(statusDot)
        statusIndicator.addSubview(statusLabel)
        containerView.addSubview(countryFlagImageView)
        containerView.addSubview(usernameLabel)
        containerView.addSubview(diamondImageView)
        containerView.addSubview(priceLabel)
        containerView.addSubview(actionButton)
        containerView.addSubview(giftImageView)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        statusIndicator.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(8))
            make.left.equalToSuperview().offset(s(8))
            make.height.equalTo(s(20))
            make.width.equalTo(s(56))
        }

        statusDot.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(s(6))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(6))
        }

        statusLabel.snp.makeConstraints { make in
            make.left.equalTo(statusDot.snp.right).offset(s(4))
            make.right.equalToSuperview().offset(s(-6))
            make.centerY.equalToSuperview()
        }
        
        countryFlagImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(8))
            make.right.equalToSuperview().offset(s(-8))
            make.width.equalTo(s(30))
            make.height.equalTo(s(20))
        }
        
        actionButton.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(s(-10))
            make.right.equalToSuperview().offset(s(-8))
            make.width.height.equalTo(s(50))
        }

        usernameLabel.snp.makeConstraints { make in
            make.top.equalTo(actionButton.snp.top)
            make.left.right.equalToSuperview().inset(s(8))
        }

        diamondImageView.snp.makeConstraints { make in
            make.bottom.equalTo(actionButton.snp.bottom)
            make.left.equalToSuperview().offset(s(6))
            make.width.height.equalTo(s(26))
        }

        priceLabel.snp.makeConstraints { make in
            make.centerY.equalTo(diamondImageView).offset(2)
            make.left.equalTo(diamondImageView.snp.right).offset(s(4))
        }
        
        giftImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(8))
            make.left.equalTo(statusIndicator.snp.right).offset(s(8))
            make.width.height.equalTo(s(24))
        }
    }
    
    // MARK: - Public Methods
    func configure(with model: LNAnchorModel) {

        // 设置状态
        statusDot.backgroundColor = model.userStatus.dotColor
        statusLabel.text = model.userStatus.displayText
        statusLabel.textColor = model.userStatus.textColor

        // 设置国家旗帜
        countryFlagImageView.backgroundColor = .blue

        // 设置用户名
        usernameLabel.text = model.displayName

        // 设置价格
        priceLabel.text = "\(model.priceDisplay)"

        // 设置操作按钮 - 根据状态设置图标
        switch model.userStatus {
        case .online, .busy:
            // Online 和 Busy 状态显示视频图标
            actionButton.setImage(UIImage(named: "ic_home_video"), for: .normal)
            actionButton.setTitleColor(.clear, for: .normal)
            actionButton.setTitle("", for: .normal)
            actionButton.tintColor = .white
        case .offline:
            // Offline 状态显示聊天图标
            actionButton.setImage(UIImage(named: "ic_home_chat"), for: .normal)
            actionButton.setTitleColor(.clear, for: .normal)
            actionButton.setTitle("", for: .normal)
            actionButton.tintColor = .white
        }

        // 设置礼物图标
        if model.hasGift {
            giftImageView.isHidden = false
            giftImageView.image = UIImage(systemName: "gift.fill")
            giftImageView.tintColor = UIColor.hex(hexString: "#FF77C9")
        } else {
            giftImageView.isHidden = true
        }
    }
}
