//
//  LNImageLoaderTests.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/8/23.
//

import UIKit

/// LNImageLoader 测试类
/// 用于验证图片加载工具的功能
class LNImageLoaderTests {
    
    /// 测试基本图片加载
    static func testBasicImageLoading() {
        print("🧪 测试基本图片加载")
        
        let imageView = UIImageView(frame: CGRect(x: 0, y: 0, width: 100, height: 100))
        let testURL = "https://picsum.photos/200/200"
        
        LNImageLoader.loadImage(imageView, url: testURL) { result in
            switch result {
            case .success(let value):
                print("✅ 基本图片加载成功: \(value.source)")
            case .failure(let error):
                print("❌ 基本图片加载失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 测试头像加载
    static func testAvatarLoading() {
        print("🧪 测试头像加载")
        
        let avatarImageView = UIImageView(frame: CGRect(x: 0, y: 0, width: 80, height: 80))
        let avatarURL = "https://picsum.photos/100/100"
        
        LNImageLoader.loadAvatar(avatarImageView, url: avatarURL) { result in
            switch result {
            case .success(let value):
                print("✅ 头像加载成功: \(value.source)")
            case .failure(let error):
                print("❌ 头像加载失败: \(error.localizedDescription)")
            }
        }
    }
    
    /// 测试便捷扩展方法
    static func testConvenienceMethods() {
        print("🧪 测试便捷扩展方法")
        
        let imageView = UIImageView()
        let thumbnailView = UIImageView()
        let coverView = UIImageView()
        
        // 测试便捷方法
        imageView.ln_setImage(url: "https://picsum.photos/150/150")
        thumbnailView.ln_setThumbnail(url: "https://picsum.photos/100/100")
        coverView.ln_setCover(url: "https://picsum.photos/300/200")
        
        print("✅ 便捷方法调用完成")
    }
    
    /// 测试无效URL处理
    static func testInvalidURL() {
        print("🧪 测试无效URL处理")
        
        let imageView = UIImageView()
        
        // 测试空URL
        LNImageLoader.loadImage(imageView, url: nil)
        LNImageLoader.loadImage(imageView, url: "")
        
        // 测试无效URL
        LNImageLoader.loadImage(imageView, url: "invalid-url") { result in
            switch result {
            case .success:
                print("❌ 无效URL不应该成功")
            case .failure(let error):
                print("✅ 无效URL正确处理: \(error.localizedDescription)")
            }
        }
    }
    
    /// 测试缓存功能
    static func testCacheManagement() {
        print("🧪 测试缓存管理")
        
        // 获取缓存大小
        LNImageLoader.getCacheSize { size in
            let sizeInMB = Double(size) / 1024.0 / 1024.0
            print("📦 当前缓存大小: \(String(format: "%.2f", sizeInMB)) MB")
        }
        
        // 测试预加载
        let testURLs = [
            "https://picsum.photos/200/200?random=1",
            "https://picsum.photos/200/200?random=2",
            "https://picsum.photos/200/200?random=3"
        ]
        LNImageLoader.preloadImages(testURLs)
        print("✅ 预加载测试完成")
        
        // 清除内存缓存
        LNImageLoader.clearMemoryCache()
        print("✅ 内存缓存清除完成")
    }
    
    /// 测试主播模型中的图片加载
    static func testAnchorModelImageLoading() {
        print("🧪 测试主播模型图片加载")
        
        // 创建测试主播模型
        let anchor = LNAnchorModel()
        anchor.id = 12345
        anchor.nickName = "Test User"
        anchor.headFileName = "https://picsum.photos/100/100?random=avatar"
        anchor.coverVideoUrl = "https://picsum.photos/300/200?random=cover"
        
        // 测试头像加载
        let avatarImageView = UIImageView()
        if anchor.headFileName.hasPrefix("http") {
            avatarImageView.ln_setAvatar(url: anchor.headFileName)
            print("✅ 主播头像加载调用完成")
        }
        
        // 测试封面加载
        let coverImageView = UIImageView()
        if !anchor.coverVideoUrl.isEmpty {
            coverImageView.ln_setCover(url: anchor.coverVideoUrl)
            print("✅ 主播封面加载调用完成")
        }
    }
    
    /// 运行所有测试
    static func runAllTests() {
        print("🚀 开始LNImageLoader测试\n")
        
        testBasicImageLoading()
        print("")
        
        testAvatarLoading()
        print("")
        
        testConvenienceMethods()
        print("")
        
        testInvalidURL()
        print("")
        
        testCacheManagement()
        print("")
        
        testAnchorModelImageLoading()
        print("")
        
        print("✅ 所有测试完成")
    }
    
    /// 性能测试
    static func performanceTest() {
        print("🧪 性能测试")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        let imageViews = (0..<10).map { _ in UIImageView() }
        
        for (index, imageView) in imageViews.enumerated() {
            let url = "https://picsum.photos/200/200?random=\(index)"
            imageView.ln_setImage(url: url)
        }
        
        let endTime = CFAbsoluteTimeGetCurrent()
        let timeElapsed = endTime - startTime
        
        print("⏱️ 10张图片加载调用耗时: \(String(format: "%.3f", timeElapsed)) 秒")
    }
}

// MARK: - 使用示例
/*
 在需要测试的地方调用：
 
 // 运行所有测试
 LNImageLoaderTests.runAllTests()
 
 // 运行单个测试
 LNImageLoaderTests.testBasicImageLoading()
 
 // 性能测试
 LNImageLoaderTests.performanceTest()
 
 // 在ViewController中测试
 override func viewDidLoad() {
     super.viewDidLoad()
     
     #if DEBUG
     LNImageLoaderTests.runAllTests()
     #endif
 }
 */
