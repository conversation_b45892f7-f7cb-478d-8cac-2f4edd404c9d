//
//  LNLiveStreamModel.swift
//  LiveNow
//
//  Created by ji<PERSON><PERSON> on 2025/8/16.
//

import Foundation
import UIKit
import HandyJSON

/// 用户状态枚举
enum LNUserStatus {
    case online
    case offline
    case busy

    var displayText: String {
        switch self {
        case .online:
            return "Online"
        case .offline:
            return "Offline"
        case .busy:
            return "Busy"
        }
    }

    var dotColor: UIColor {
        switch self {
        case .online:
            return UIColor.hex(hexString: "#00FF91")
        case .offline:
            return UIColor.hex(hexString: "#DADADA")
        case .busy:
            return UIColor.hex(hexString: "#FF1500")
        }
    }

    var textColor: UIColor {
        switch self {
        case .online, .offline:
            return .white
        case .busy:
            return UIColor.hex(hexString: "#FF1500")
        }
    }
}

/// 主播数据模型 - 直接匹配API返回的数据结构
class LNAnchorModel: BaseModel {
    var age: Int = 0
    var birthday: String = ""
    var constellation: Int = 0
    var country: String = ""
    var coverVideoUrl: String = ""
    var followFlag: String = ""
    var gender: String = ""
    var groundFileName: String = ""
    var headFileName: String = ""
    var hotAnchor: String = ""
    var id: Int = 0
    var incomeDiamond: Int = 0
    var isNeedMute: Bool = true
    var isTop: String = ""
    var isVirVideo: String = ""
    var language: String = ""
    var needMuteStr: String = ""
    var nickName: String = ""
    var normalHeadFileName: String = ""
    var onlineStatus: String = ""
    var repeatId: Int = 0
    var roomTitle: String = ""
    var showVideoUrl: String = ""
    var source: Int = 0
    var unionId: Int = 0
    var userCode: String = ""
    var videoPrice: Int = 0
    var vipFlag: String = ""
    var virVideoId: Int = 0

    /// 计算用户状态
    var userStatus: LNUserStatus {
        switch onlineStatus {
        case "1":
            return .online
        case "2":
            return .busy
        default:
            return .offline
        }
    }

    /// 计算国家旗帜
    var countryFlag: String {
        switch country.lowercased() {
        case "palestine", "ps":
            return "🇵🇸"
        case "jordan", "jo":
            return "🇯🇴"
        case "indonesia", "id":
            return "🇮🇩"
        case "philippines", "ph":
            return "🇵🇭"
        case "vietnam", "vn":
            return "🇻🇳"
        case "thailand", "th":
            return "🇹🇭"
        default:
            return "🇺🇸"
        }
    }

    /// 计算显示名称
    var displayName: String {
        return nickName.isEmpty ? "NIKENAME" : nickName
    }

    /// 计算价格显示
    var priceDisplay: String {
        return videoPrice > 0 ? "\(videoPrice)/min" : "60/min"
    }

    /// 计算头像名称
    var avatarName: String {
        return headFileName.isEmpty ? "avatar\(id % 6 + 1)" : headFileName
    }

    /// 是否支持视频通话
    var hasVideoCall: Bool {
        return videoPrice > 0
    }

    /// 是否有礼物功能
    var hasGift: Bool {
        return incomeDiamond > 0
    }
}

/// 主播列表分页响应模型
class LNAnchorListResponse: BaseModel {
    var current: Int = 0
    var hitCount: Bool = true
    var pages: Int = 0
    var records: [LNAnchorModel] = []
    var searchCount: Bool = true
    var size: Int = 0
    var total: Int = 0
}

/// 主播列表API响应模型
class LNAnchorApiResponse: BaseModel {
    var code: Int = 0
    var data: LNAnchorListResponse?
    var msg: String = ""
    var success: Bool = false
}


